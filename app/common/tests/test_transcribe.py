"""Concise tests for common.models.transcribe module."""

import io

import pytest
from fastapi import UploadFile
from pydantic import ValidationError

from common.models.transcribe import TranscriptionRequest, TranscriptionJobResponse


class TestTranscriptionRequest:
    """Test cases for TranscriptionRequest model."""

    def test_with_upload_file(self):
        """Test TranscriptionRequest with UploadFile."""
        upload_file = UploadFile(
            filename="test_audio.mp3",
            file=io.BytesIO(b"mock audio content")
        )

        request = TranscriptionRequest(
            file=upload_file,
            language_code="en-US",
            media_format="mp3",
            user="test_user"
        )

        assert request.file == upload_file
        assert request.language_code == "en-US"
        assert request.media_format == "mp3"
        assert request.user == "test_user"

    def test_with_string_url(self):
        """Test TranscriptionRequest with S3 URL string."""
        s3_url = "s3://bucket/audio.wav"

        request = TranscriptionRequest(
            file=s3_url,
            language_code="es-ES",
            media_format="wav",
            user="user123"
        )

        assert request.file == s3_url
        assert request.language_code == "es-ES"
        assert request.media_format == "wav"
        assert request.user == "user123"

    def test_minimal_fields(self):
        """Test TranscriptionRequest with only required field."""
        s3_url = "s3://bucket/audio/file.mp3"
        request = TranscriptionRequest(file=s3_url)

        assert request.file == s3_url
        assert request.language_code is None
        assert request.media_format is None
        assert request.user is None

    def test_missing_file_validation(self):
        """Test TranscriptionRequest validation without file."""
        with pytest.raises(ValidationError) as exc_info:
            TranscriptionRequest(language_code="en-US", user="test")

        assert "file" in str(exc_info.value)

    def test_json_serialization(self):
        """Test TranscriptionRequest JSON serialization."""
        s3_url = "s3://bucket/audio.mp3"
        request = TranscriptionRequest(
            file=s3_url,
            language_code="fr-FR",
            media_format="mp3",
            user="user456"
        )

        json_data = request.model_dump()
        assert json_data["file"] == s3_url
        assert json_data["language_code"] == "fr-FR"
        assert json_data["media_format"] == "mp3"
        assert json_data["user"] == "user456"

    def test_as_form_method(self):
        """Test TranscriptionRequest.as_form class method."""
        upload_file = UploadFile(
            filename="test.mp3",
            file=io.BytesIO(b"audio content")
        )

        request = TranscriptionRequest.as_form(
            file=upload_file,
            language_code="en-US",
            user="test_user"
        )

        assert request.file == upload_file
        assert request.language_code == "en-US"
        assert request.user == "test_user"

    def test_as_form_with_url(self):
        """Test as_form with file_url parameter."""
        file_url = "s3://bucket/audio.mp3"

        request = TranscriptionRequest.as_form(file_url=file_url)

        assert request.file == file_url
        assert request.language_code is None
        assert request.user is None

    def test_as_form_missing_file_and_url(self):
        """Test as_form validation when both file and file_url are missing."""
        with pytest.raises(ValueError) as exc_info:
            TranscriptionRequest.as_form(language_code="en-US", user="test")

        assert "file or file_url must be provided" in str(exc_info.value)


class TestTranscriptionJobResponse:
    """Test cases for TranscriptionJobResponse model."""

    def test_creation(self):
        """Test creating TranscriptionJobResponse."""
        job_id = "transcribe-job-12345678-1234-5678-9abc-123456789012"
        response = TranscriptionJobResponse(job_id=job_id)

        assert response.job_id == job_id
        assert isinstance(response.job_id, str)

    def test_missing_job_id_validation(self):
        """Test TranscriptionJobResponse validation without job_id."""
        with pytest.raises(ValidationError) as exc_info:
            TranscriptionJobResponse()

        assert "job_id" in str(exc_info.value)

    def test_json_serialization(self):
        """Test TranscriptionJobResponse JSON serialization."""
        job_id = "transcribe-job-abcdef123456"
        response = TranscriptionJobResponse(job_id=job_id)

        json_data = response.model_dump()
        assert json_data["job_id"] == job_id

        json_str = response.model_dump_json()
        assert job_id in json_str

    def test_equality(self):
        """Test TranscriptionJobResponse equality comparison."""
        job_id = "same-job-id"
        response1 = TranscriptionJobResponse(job_id=job_id)
        response2 = TranscriptionJobResponse(job_id=job_id)
        response3 = TranscriptionJobResponse(job_id="different-job-id")

        assert response1 == response2
        assert response1 != response3


class TestTranscribeModelsIntegration:
    """Integration tests for transcribe models."""

    def test_request_response_workflow(self):
        """Test realistic transcription workflow."""
        # Create request
        s3_url = "s3://enterprise-audio/meetings/2023-10-01/board-meeting.wav"
        request = TranscriptionRequest(
            file=s3_url,
            language_code="en-US",
            media_format="wav",
            user="enterprise_user"
        )

        # Simulate job submission response
        job_id = "transcription_job_2023_10_01_001"
        response = TranscriptionJobResponse(job_id=job_id)

        # Verify data consistency
        assert request.file == s3_url
        assert request.language_code == "en-US"
        assert response.job_id == job_id

    def test_serialization_roundtrip(self):
        """Test serialization roundtrip for response."""
        original_response = TranscriptionJobResponse(job_id="roundtrip_test_job_12345")

        # Serialize and deserialize
        json_str = original_response.model_dump_json()
        import json
        json_data = json.loads(json_str)
        recreated_response = TranscriptionJobResponse(**json_data)

        assert recreated_response.job_id == original_response.job_id
