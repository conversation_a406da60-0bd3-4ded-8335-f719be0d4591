"""Concise tests for common.database module."""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy.exc import SQLAlchemy<PERSON>rror, OperationalError
from sqlalchemy.orm import Session

from common.database import get_db_session


class TestGetDbSession:
    """Test cases for get_db_session function."""

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_session_creation_and_cleanup(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test successful database session creation and cleanup."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        # Act
        session_generator = get_db_session()
        session = next(session_generator)

        # Assert
        mock_create_engine.assert_called_once_with("postgresql://user:pass@localhost/test")
        mock_sessionmaker.assert_called_once_with(autocommit=False, autoflush=False, bind=mock_engine)
        mock_session_class.assert_called_once()
        assert session == mock_session

        # Test cleanup
        try:
            next(session_generator)
        except StopIteration:
            pass
        mock_session.close.assert_called_once()

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_session_exception_handling(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test database session exception handling and cleanup."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        # Act & Assert
        session_generator = get_db_session()
        session = next(session_generator)
        assert session == mock_session

        # Simulate exception during session usage
        try:
            session_generator.throw(SQLAlchemyError("Database error"))
        except SQLAlchemyError:
            pass

        # Verify cleanup still happens
        mock_session.close.assert_called_once()

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    def test_engine_creation_failure(self, mock_create_engine, mock_settings):
        """Test handling of engine creation failure."""
        # Arrange
        mock_settings.database.connection_string = "invalid://connection"
        mock_create_engine.side_effect = OperationalError("Connection failed", None, None)

        # Act & Assert
        with pytest.raises(OperationalError):
            session_generator = get_db_session()
            next(session_generator)

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_session_creation_failure(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test handling of session creation failure."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session_class.side_effect = SQLAlchemyError("Session creation failed")

        # Act & Assert
        with pytest.raises(SQLAlchemyError):
            session_generator = get_db_session()
            next(session_generator)

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_multiple_sessions(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test creating multiple independent sessions."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        
        # Create different mock sessions for each call
        mock_session1 = Mock(spec=Session)
        mock_session2 = Mock(spec=Session)
        mock_session_class.side_effect = [mock_session1, mock_session2]

        # Act
        session_gen1 = get_db_session()
        session1 = next(session_gen1)
        
        session_gen2 = get_db_session()
        session2 = next(session_gen2)

        # Assert
        assert session1 == mock_session1
        assert session2 == mock_session2
        assert session1 != session2
        
        # Engine should be created for each session
        assert mock_create_engine.call_count == 2
        assert mock_sessionmaker.call_count == 2

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_session_configuration(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test that session is configured correctly."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        # Act
        session_generator = get_db_session()
        next(session_generator)

        # Assert session configuration
        mock_sessionmaker.assert_called_once_with(
            autocommit=False,
            autoflush=False,
            bind=mock_engine
        )

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_connection_string_usage(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test that connection string from settings is used correctly."""
        # Test different connection strings
        test_cases = [
            "postgresql://user:pass@localhost:5432/testdb",
            "sqlite:///test.db",
            "mysql://user:pass@localhost/testdb"
        ]

        for connection_string in test_cases:
            # Arrange
            mock_settings.database.connection_string = connection_string
            mock_engine = Mock()
            mock_create_engine.return_value = mock_engine
            mock_session_class = Mock()
            mock_sessionmaker.return_value = mock_session_class
            mock_session = Mock(spec=Session)
            mock_session_class.return_value = mock_session

            # Reset mocks
            mock_create_engine.reset_mock()

            # Act
            session_generator = get_db_session()
            next(session_generator)

            # Assert
            mock_create_engine.assert_called_once_with(connection_string)

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_generator_protocol(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test that get_db_session follows generator protocol correctly."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        # Act
        session_generator = get_db_session()
        
        # Test that it's a generator
        assert hasattr(session_generator, '__next__')
        assert hasattr(session_generator, '__iter__')
        
        # Test first yield
        session = next(session_generator)
        assert session == mock_session
        
        # Test that second next() raises StopIteration (cleanup)
        with pytest.raises(StopIteration):
            next(session_generator)
        
        # Verify cleanup was called
        mock_session.close.assert_called_once()


class TestDatabaseIntegration:
    """Integration tests for database functionality."""

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_realistic_usage_pattern(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test realistic database usage pattern."""
        # Arrange
        mock_settings.database.connection_string = "*******************************/production"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        # Simulate some database operations
        mock_session.query.return_value.filter.return_value.first.return_value = {"id": 1, "name": "test"}
        mock_session.commit.return_value = None

        # Act - Simulate typical usage
        session_generator = get_db_session()
        session = next(session_generator)
        
        # Simulate database operations
        result = session.query().filter().first()
        session.commit()
        
        # Cleanup
        try:
            next(session_generator)
        except StopIteration:
            pass

        # Assert
        assert result == {"id": 1, "name": "test"}
        mock_session.query.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.close.assert_called_once()
