"""Database-related test fixtures."""

from typing import Any, Generator

import pytest
from sqlalchemy import create_engine
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession

from common.services import JobStore, PostgresChatStore, AgentStore, PromptStore


# Database fixtures
# Database URL constants - used by session fixtures
TEST_DB_URL = "sqlite:///:memory:"
TEST_ASYNC_DB_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture
def db_session() -> Generator[Any, None, None]:
    """Create a test database session."""
    engine = create_engine(TEST_DB_URL, echo=False)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    yield SessionLocal
    engine.dispose()


@pytest.fixture
def async_db_session() -> Generator[Any, None, None]:
    """Create a test async database session."""
    engine = create_async_engine(TEST_ASYNC_DB_URL, echo=False)
    AsyncSessionLocal = sessionmaker(
        bind=engine,
        class_=AsyncSession,
        autocommit=False,
        autoflush=False,
    )
    yield AsyncSessionLocal
    engine.sync_engine.dispose()


# Service fixtures - only keep the ones actually used
@pytest.fixture
def job_store(db_session: Any, async_db_session: Any) -> JobStore:
    """Create a JobStore instance for testing."""
    return JobStore(
        session=db_session,
        async_session=async_db_session,
        table_name="test_batch_jobs",
        schema_name="public",
        use_jsonb=False,
    )
