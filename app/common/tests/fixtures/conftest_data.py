"""Test data-related fixtures."""

import uuid
from datetime import datetime
from typing import Dict, Any

import pytest

from common.enums import BatchJobStatusEnum


@pytest.fixture(scope="session")
def json_test_data() -> Dict[str, str]:
    """Sample JSON data for parser testing."""
    return {
        "valid_json": '{"name": "test", "value": 123}',
        "json_in_markdown": '```json\n{"name": "test", "value": 123}\n```',
        "json_with_header": 'Here is your result:\n{"name": "test", "value": 123}',
        "invalid_json": '{"name": "test", "value":}',
        "malformed_json": '{"name": "test" "value": 123}',
    }


# Test data fixtures
@pytest.fixture
def sample_client_guid() -> uuid.UUID:
    """Sample client GUID for testing."""
    return uuid.UUID("12345678-1234-5678-9abc-123456789012")


@pytest.fixture
def sample_completion_request() -> Dict[str, Any]:
    """Sample CompletionRequest data for testing."""
    return {
        "model": "gpt-3.5-turbo",
        "messages": ["Test completion request"],
        "max_tokens": 100,
        "temperature": 0.7
    }


@pytest.fixture
def sample_chat_completion_request() -> Dict[str, Any]:
    """Sample ChatCompletionRequest data for testing."""
    return {
        "model": "gpt-4",
        "messages": [{"role": "user", "content": "Test chat message"}],
        "max_tokens": 150,
        "temperature": 0.8
    }


@pytest.fixture
def sample_user_guid() -> uuid.UUID:
    """Sample user GUID for testing."""
    return uuid.UUID("*************-8765-cba9-************")


@pytest.fixture
def fake_batch_job(
    sample_client_guid: uuid.UUID,
    sample_user_guid: uuid.UUID
) -> Dict[str, Any]:
    """Fixture to generate a fake batch job."""
    return {
        "job_id": uuid.uuid4(),  # Return actual UUID, not string
        "status": BatchJobStatusEnum.PENDING.value,
        "client_guid": str(sample_client_guid),
        "user_guid": str(sample_user_guid),
        "job_params": {"param1": "value1", "param2": "value2"},
        "results": None,
        "processed_count": 0,
        "total_count": 100,
        "created_at": "2023-10-01T00:00:00Z",
        "updated_at": "2023-10-01T00:00:00Z",
        "started_at": None,
        "completed_at": None,
    }
